#!/usr/bin/env python3
"""
Script to copy the generated bitcoin_predictions.csv file to the frontend public directory
This ensures the frontend can access the CSV file via HTTP requests
"""

import os
import shutil
import sys

def copy_csv_to_public():
    """Copy the CSV file from Data/ to Frontend/public/Data/"""
    
    # Define paths
    source_path = 'Data/bitcoin_predictions.csv'
    destination_dir = 'Frontend/public/Data'
    destination_path = os.path.join(destination_dir, 'bitcoin_predictions.csv')
    
    # Check if source file exists
    if not os.path.exists(source_path):
        print(f"❌ Error: Source file not found: {source_path}")
        print("Please run the model training script first:")
        print("  python AI/bitcoin_price_prediction_using_lstm.py")
        return False
    
    # Create destination directory if it doesn't exist
    os.makedirs(destination_dir, exist_ok=True)
    
    try:
        # Copy the file
        shutil.copy2(source_path, destination_path)
        
        # Verify the copy was successful
        if os.path.exists(destination_path):
            source_size = os.path.getsize(source_path)
            dest_size = os.path.getsize(destination_path)
            
            print(f"✅ Successfully copied CSV file:")
            print(f"   From: {source_path} ({source_size} bytes)")
            print(f"   To:   {destination_path} ({dest_size} bytes)")
            
            if source_size == dest_size:
                print("✅ File sizes match - copy verified")
                return True
            else:
                print("⚠️  Warning: File sizes don't match")
                return False
        else:
            print(f"❌ Error: Destination file was not created: {destination_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error copying file: {str(e)}")
        return False

if __name__ == "__main__":
    print("Bitcoin Predictions CSV Copy Utility")
    print("=" * 50)
    
    success = copy_csv_to_public()
    
    if success:
        print("\n🎉 CSV file is now ready for the frontend!")
        print("You can now start the frontend with:")
        print("  cd Frontend && npm start")
    else:
        print("\n❌ Failed to copy CSV file.")
        print("Please check the error messages above and try again.")
        sys.exit(1)
