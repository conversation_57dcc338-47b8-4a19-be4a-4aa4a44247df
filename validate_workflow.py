#!/usr/bin/env python3
"""
Comprehensive workflow validation script for the Bitcoin Price Prediction System
Tests the complete flow from CSV generation to frontend compatibility
"""

import os
import pandas as pd
import sys
from datetime import datetime, <PERSON><PERSON><PERSON>

def validate_csv_structure(csv_path):
    """Validate the structure and content of the CSV file"""
    print(f"\n📊 Validating CSV structure: {csv_path}")
    
    if not os.path.exists(csv_path):
        print(f"❌ CSV file not found: {csv_path}")
        return False
    
    try:
        # Read CSV file
        df = pd.read_csv(csv_path)
        
        # Check required columns
        required_columns = ['Date', 'Predicted_Price']
        if not all(col in df.columns for col in required_columns):
            print(f"❌ Missing required columns. Expected: {required_columns}, Found: {list(df.columns)}")
            return False
        
        # Check data types and format
        df['Date'] = pd.to_datetime(df['Date'])
        df['Predicted_Price'] = pd.to_numeric(df['Predicted_Price'])
        
        # Validate data integrity
        if df.empty:
            print("❌ CSV file is empty")
            return False
        
        if df['Date'].isnull().any():
            print("❌ Found null dates in CSV")
            return False
        
        if df['Predicted_Price'].isnull().any():
            print("❌ Found null prices in CSV")
            return False
        
        # Check date sequence
        dates_sorted = df['Date'].is_monotonic_increasing
        if not dates_sorted:
            print("❌ Dates are not in chronological order")
            return False
        
        # Check for expected number of predictions (should be 1095 for 3 years)
        expected_count = 1095
        actual_count = len(df)
        
        print(f"✅ CSV structure validation passed:")
        print(f"   - Columns: {list(df.columns)}")
        print(f"   - Records: {actual_count} (expected: {expected_count})")
        print(f"   - Date range: {df['Date'].min().date()} to {df['Date'].max().date()}")
        print(f"   - Price range: ${df['Predicted_Price'].min():.2f} - ${df['Predicted_Price'].max():.2f}")
        
        if actual_count != expected_count:
            print(f"⚠️  Warning: Expected {expected_count} predictions, found {actual_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error validating CSV: {str(e)}")
        return False

def validate_frontend_accessibility(csv_path):
    """Validate that the CSV is accessible to the frontend"""
    print(f"\n🌐 Validating frontend accessibility")
    
    frontend_csv_path = 'Frontend/public/Data/bitcoin_predictions.csv'
    
    if not os.path.exists(frontend_csv_path):
        print(f"❌ Frontend CSV not found: {frontend_csv_path}")
        print("   Run: python copy_csv_to_public.py")
        return False
    
    # Compare file sizes
    try:
        original_size = os.path.getsize(csv_path)
        frontend_size = os.path.getsize(frontend_csv_path)
        
        if original_size != frontend_size:
            print(f"⚠️  File size mismatch: Original({original_size}) vs Frontend({frontend_size})")
        else:
            print(f"✅ Frontend CSV accessible: {frontend_csv_path} ({frontend_size} bytes)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking frontend accessibility: {str(e)}")
        return False

def validate_date_range_functionality(csv_path):
    """Validate date range filtering functionality"""
    print(f"\n📅 Validating date range functionality")
    
    try:
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date'])
        
        # Test various date ranges
        test_cases = [
            ("1 month", 30),
            ("6 months", 180),
            ("1 year", 365),
            ("3 years", 1095)
        ]
        
        for name, days in test_cases:
            filtered_df = df.head(days)
            if len(filtered_df) > 0:
                print(f"✅ {name} filter: {len(filtered_df)} records")
            else:
                print(f"❌ {name} filter: No data")
                return False
        
        # Test custom date range
        start_date = df['Date'].min()
        end_date = start_date + timedelta(days=90)
        custom_filtered = df[(df['Date'] >= start_date) & (df['Date'] <= end_date)]
        print(f"✅ Custom date range: {len(custom_filtered)} records")
        
        return True
        
    except Exception as e:
        print(f"❌ Error validating date range functionality: {str(e)}")
        return False

def validate_model_files():
    """Validate that required model files exist"""
    print(f"\n🤖 Validating model files")
    
    model_files = [
        'AI/model/bitcoin_advanced_multivariate_lstm.keras',
        'AI/model/bitcoin_price_scaler.save',
        'AI/model/bitcoin_volume_scaler.save'
    ]
    
    all_exist = True
    for file_path in model_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ Missing: {file_path}")
            all_exist = False
    
    return all_exist

def validate_frontend_dependencies():
    """Validate frontend dependencies and structure"""
    print(f"\n⚛️  Validating frontend structure")
    
    required_files = [
        'Frontend/package.json',
        'Frontend/src/components/BitcoinPriceChart.tsx',
        'Frontend/src/components/DateRangeSelector.tsx',
        'Frontend/src/api/csvDataService.ts',
        'Frontend/src/types/index.ts'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            all_exist = False
    
    # Check that axios is removed from package.json
    try:
        with open('Frontend/package.json', 'r') as f:
            package_content = f.read()
            if 'axios' in package_content:
                print("⚠️  Warning: axios still present in package.json (should be removed)")
            else:
                print("✅ axios dependency removed")
    except:
        pass
    
    return all_exist

def main():
    """Run complete workflow validation"""
    print("🚀 Bitcoin Price Prediction System - Workflow Validation")
    print("=" * 70)
    
    csv_path = 'Data/bitcoin_predictions.csv'
    
    # Run all validation tests
    tests = [
        ("Model Files", validate_model_files),
        ("CSV Structure", lambda: validate_csv_structure(csv_path)),
        ("Frontend Accessibility", lambda: validate_frontend_accessibility(csv_path)),
        ("Date Range Functionality", lambda: validate_date_range_functionality(csv_path)),
        ("Frontend Dependencies", validate_frontend_dependencies)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*70}")
    print("📋 VALIDATION SUMMARY")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All validations passed! The system is ready for use.")
        print("\nNext steps:")
        print("1. Start the frontend: cd Frontend && npm start")
        print("2. Open http://localhost:3000 in your browser")
        print("3. Test different timeframes and custom date ranges")
    else:
        print(f"\n⚠️  {len(results) - passed} validation(s) failed. Please address the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
